<!-- eslint-disable max-lines -->
<!-- 会议详情页 -->
<template>
    <div class="box-main" ref="box">
        <el-button type="primary" @click="goBack" class="back-button"
            >返回</el-button
        >
        <div class="meeting-selector-wrapper">
            <div class="meeting-selector-container">
                <div
                    v-if="meetingInfo.organizeForm === '线上'"
                    class="online-review-tag"
                >
                    【线上评审】
                </div>
                <el-select
                    class="history-select"
                    v-model="meetingInfo.meetingTitle"
                    size="medium"
                    @change="handleMeetingChange"
                    placeholder="请选择会议"
                >
                    <el-option
                        v-for="i in relatedMeetingList"
                        :label="i.meetingTitle"
                        :value="i.meetingId"
                        :key="i.meetingId"
                    ></el-option>
                </el-select>
                <span class="meeting-status" v-if="meetingInfo.meetingStatus">
                    {{ meetingInfo.meetingStatus }}
                    <el-popover
                        v-if="meetingInfo.meetingStatus === '已取消'"
                        ref="popover1"
                        placement="top-start"
                        width="300"
                        trigger="hover"
                    >
                        <svg-icon
                            slot="reference"
                            icon-class="feature-meeting-cancel-info"
                            class="meeting-status-info-icon"
                        ></svg-icon>
                        <div style="font-weight: bold">取消原因：</div>
                        <div>{{ meetingInfo.cancelReason || '' }}</div>
                        <div style="font-weight: bold">下一步工作：</div>
                        <div>{{ meetingInfo.nextStep || '' }}</div>
                    </el-popover>
                </span>
                <span>|</span>
                <span>
                    纪要状态：
                    {{ meetingInfo.minutesStatus || '' }}
                </span>
            </div>
        </div>
        <div ref="content">
            <el-card class="meeting-info-card" shadow="never">
                <!-- 标题行 -->
                <div class="header-row">
                    <div class="meeting-title">基本信息</div>
                    <el-link
                        v-if="combinedPermission"
                        type="primary"
                        @click="openDialog('编辑会议')"
                        :style="{
                            visibility: isPrint ? 'hidden' : ''
                        }"
                        class="meeting-edit"
                        >编辑</el-link
                    >
                </div>

                <!-- 会议信息表格 -->
                <el-row class="info-table">
                    <el-col :span="6" class="info-item">
                        <div class="label">会议类型</div>
                        <div class="content">
                            {{ meetingInfo.meetingType }}
                        </div>
                    </el-col>
                    <el-col :span="6" class="info-item">
                        <div class="label">会议材料</div>
                        <div
                            v-if="!meetingInfo.meetingMaterials"
                            class="content"
                        >
                            {{ meetingInfo.materialsGrantFlag }}
                        </div>
                        <el-button type="text" v-else>
                            <a
                                :href="meetingInfo.meetingMaterials"
                                noopener
                                noreferrer
                                target="_blank"
                                class="content"
                                style="font-size: 14px"
                                >查看</a
                            >
                        </el-button>
                    </el-col>
                    <el-col :span="6" class="info-item">
                        <div class="label">会议时间</div>
                        <div class="content">
                            {{ meetingTime }}
                        </div>
                    </el-col>
                    <el-col :span="6" class="info-item">
                        <div class="label">会议地点</div>
                        <div class="content">
                            {{ meetingInfo.meetingLocation }}
                        </div>
                    </el-col>
                    <el-col :span="6" class="info-item">
                        <div class="label">会议组织</div>
                        <div class="content">
                            {{ organizer }}
                        </div>
                    </el-col>
                    <el-col :span="6" class="info-item">
                        <div class="label">主持人</div>
                        <div class="content">
                            {{ host }}
                        </div>
                    </el-col>
                    <el-col :span="6" class="info-item">
                        <div class="label">会议纪要编制</div>
                        <div class="content">
                            {{ minutesEditor || '' }}
                        </div>
                    </el-col>
                    <el-col :span="6" class="info-item">
                        <div class="label">会议纪要审核</div>
                        <div class="content">{{ minutesChecker || '' }}</div>
                    </el-col>
                </el-row>

                <!-- 评审委员表格 -->
                <div
                    class="committee-section"
                    v-show="meetingClass === '评审会议'"
                >
                    <div class="label">参会评审委员</div>
                    <div class="content">
                        <div class="w-50">
                            <div
                                v-for="(item, index) in leftCommittee"
                                :key="index"
                                class="committee-item"
                            >
                                <span class="role">{{ item.userRole }}：</span>
                                <span class="name">{{ item.userName }}</span>
                                <span
                                    class="status"
                                    v-if="item.attendanceStatus"
                                    >{{ item.attendanceStatus }}</span
                                >
                                <span
                                    v-if="item.replaceUserName"
                                    class="alternative"
                                    >替代人： {{ item.replaceUserName }}</span
                                >
                            </div>
                        </div>
                        <div class="w-50">
                            <div
                                v-for="(item, index) in rightCommittee"
                                :key="index"
                                class="committee-item"
                            >
                                <span class="role">{{ item.userRole }}：</span>
                                <span class="name">{{ item.userName }}</span>
                                <span
                                    class="status"
                                    v-if="item.attendanceStatus"
                                    >{{ item.attendanceStatus }}</span
                                >
                                <span
                                    v-if="item.replaceUserName"
                                    class="alternative"
                                    >替代人： {{ item.replaceUserName }}</span
                                >
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 参会人员 -->
                <div class="participants-section">
                    <div class="label">参会人员</div>
                    <div class="content">{{ attendacePeople.join('；') }}</div>
                </div>

                <!-- 关联项目 -->
                <div class="related-projects">
                    <div class="label">关联项目</div>
                    <div class="content">
                        {{ assProjectNameList.join('; ') }}
                    </div>
                </div>

                <!-- 会议议程 -->
                <div class="agenda-section">
                    <div class="label">会议议程</div>
                    <div class="content">{{ meetingInfo.meetingAgenda }}</div>
                </div>
            </el-card>

            <el-table
                v-show="
                    meetingClass === '评审会议' &&
                    meetingInfo.organizeForm === '线下'
                "
                :data="preReviewList"
                class="table mt-15"
                empty-text="无预审意见"
            >
                <el-table-column header-align="center" prop="prop">
                    <template #header>
                        <div
                            class="flex header-title"
                            v-show="
                                meetingClass === '评审会议' &&
                                meetingInfo.organizeForm === '线下'
                            "
                        >
                            会前预审意见
                            <el-link
                                v-if="combinedPermission"
                                @click="openDialog('反馈预审意见')"
                                class="edit-btn"
                                :style="{
                                    visibility: isPrint ? 'hidden' : ''
                                }"
                                >编辑</el-link
                            >
                        </div>
                    </template>
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="creatorName"
                        label="提出人"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="prepContent"
                        label="预审意见"
                    >
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="responsibleName"
                        label="责任人"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="finishDate"
                        label="完成时间"
                        width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="finishStatus"
                        label="状态"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        align="left"
                        prop="finishDesc"
                        label="完成情况"
                    >
                    </el-table-column>
                </el-table-column>
            </el-table>

            <el-table
                v-show="
                    meetingClass === '评审会议' &&
                    meetingInfo.organizeForm === '线上'
                "
                class="table mt-15"
                :data="onlineReviewList"
                :span-method="onlineObjectSpanMethod"
                empty-text="无线上评审意见"
            >
                <el-table-column header-align="center" prop="prop">
                    <template #header>
                        <div
                            class="flex header-title"
                            v-show="
                                meetingClass === '评审会议' &&
                                meetingInfo.organizeForm === '线上'
                            "
                        >
                            线上评审意见
                            <el-link
                                type="primary"
                                @click="getShareLink"
                                class="edit-btn-share-link"
                                :style="{
                                    visibility: isPrint ? 'hidden' : ''
                                }"
                                >分享链接</el-link
                            >
                            <el-link
                                v-if="combinedPermission"
                                type="primary"
                                @click="openDialog('线上评审')"
                                class="edit-btn"
                                :style="{
                                    visibility: isPrint ? 'hidden' : ''
                                }"
                                >编辑</el-link
                            >
                        </div>
                    </template>
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="reviewItem"
                        label="项"
                        width="130"
                    >
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        align="center"
                        label="结论"
                        width="200"
                    >
                        <template slot-scope="scope">{{
                            `${scope.row.reviewConclusion}${
                                scope.row.reviewDate
                                    ? ` [${scope.row.reviewDate}]`
                                    : ''
                            }`
                        }}</template>
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        prop="additionalView"
                        label="附加意见"
                    >
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        align="judgesName"
                        prop="prop"
                        label="评委签字"
                        width="400"
                    >
                        <template #default="scoped">
                            <div class="flex">
                                <div style="width: 50%; text-align: right">
                                    {{ scoped.row.judgesRole }}
                                </div>
                                <div style="margin-left: 10px">
                                    {{
                                        scoped.row.reviewConclusion
                                            ? scoped.row.judgesName
                                            : ''
                                    }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table>

            <el-table
                v-if="showMinutesList"
                class="table mt-15"
                :data="minutesList"
                :span-method="minutesObjectSpanMethod"
                empty-text="无会议纪要"
                :cell-style="{ verticalAlign: 'top' }"
            >
                <el-table-column header-align="center" prop="prop">
                    <template #header>
                        <div class="flex header-title">
                            会议纪要
                            <el-link
                                v-if="
                                    isOrganizerOrWriterHasPermission ||
                                    dataManagerPermission
                                "
                                type="primary"
                                @click="openDialog('编辑纪要')"
                                class="edit-btn"
                                :style="{
                                    visibility: isPrint ? 'hidden' : ''
                                }"
                                >编辑</el-link
                            >
                        </div>
                    </template>
                    <el-table-column
                        prop="prop"
                        v-if="
                            meetingClass === '评审会议' &&
                            meetingInfo.meetingConclusion
                        "
                    >
                        <template slot="header">
                            <div
                                class="flex"
                                style="
                                    align-items: center;
                                    justify-content: center;
                                    min-width: 100%;
                                "
                            >
                                <div style="width: 300px; text-align: center">
                                    会议结论
                                </div>
                                <div class="flex conclusion">
                                    <div style="border-bottom: 1px solid black">
                                        <div style="padding-left: 15px">
                                            {{ meetingInfo.meetingConclusion }}
                                        </div>
                                    </div>
                                    <div style="padding-left: 15px">
                                        {{ meetingInfo.meetingConclusionView }}
                                    </div>
                                </div>
                            </div>
                        </template>
                        <el-table-column
                            header-align="center"
                            prop="problemItem"
                            label="针对问题/事项"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            label="提出人"
                            width="80"
                        >
                            <template slot-scope="scope">
                                <div class="pre-line">
                                    {{
                                        scope.row.creatorName
                                            .split(' ')
                                            .join('\n')
                                    }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            prop="meetingRequire"
                            label="会议要求"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="responsibleName"
                            label="责任人"
                            width="80"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="planFinishDate"
                            label="计划完成时间"
                            width="100"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="finishStatus"
                            label="状态"
                            width="80"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="left"
                            prop="finishDesc"
                            label="完成情况"
                        >
                        </el-table-column>
                    </el-table-column>
                    <template v-else>
                        <el-table-column
                            header-align="center"
                            prop="problemItem"
                            label="针对问题/事项"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="creatorName"
                            label="提出人"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            prop="meetingRequire"
                            label="会议要求"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="responsibleName"
                            label="责任人"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="planFinishDate"
                            label="计划完成时间"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="finishStatus"
                            label="状态"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="left"
                            prop="finishDesc"
                            label="完成情况"
                        >
                        </el-table-column>
                    </template>
                </el-table-column>
            </el-table>

            <el-table
                class="table mt-15"
                :data="qualityInfoList"
                v-if="
                    meetingInfo.meetingType === '一级TR评审' &&
                    meetingInfo.organizeForm === '线下'
                "
                :span-method="qualityReviewObjectSpanMethod"
                empty-text="无会议效果评价"
            >
                <el-table-column header-align="center" prop="prop">
                    <template #header>
                        <div class="flex header-title">
                            会议效果评价
                            <el-link
                                v-if="
                                    (isOrganizerOrWriterHasPermission &&
                                        isQualityFinish) ||
                                    dataManagerPermission
                                "
                                type="primary"
                                @click="openDialog('反馈会议质量')"
                                class="edit-btn"
                                :style="{
                                    visibility: isPrint ? 'hidden' : ''
                                }"
                                >编辑</el-link
                            >
                        </div>
                    </template>
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="evaluationItems"
                        label="评价项"
                        width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="evaluationName"
                        label="评价人"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        prop="evaluationScore"
                        label="评价结论"
                    >
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="thingsToImprove"
                        label="待改善事项"
                    >
                    </el-table-column>
                </el-table-column>
            </el-table>
        </div>
        <div class="flex mt-15" style="justify-content: center">
            <el-button type="primary" @click="getPdf">导出为pdf</el-button>
        </div>

        <MeetingUpdate
            :visible.sync="meetingUpdateVisible"
            :title="meetingUpdateTitle"
            :meetingId="meetingId"
            @update="update"
        ></MeetingUpdate>
        <MeetingMinutes
            :visible.sync="meetingMinutesVisible"
            :title="meetingMinutesTitle"
            :meetingId="meetingId"
            @update="update"
        ></MeetingMinutes>
        <PreReviewOpinion
            :visible.sync="preReviewOpinionVisible"
            :meetingId="meetingId"
            @update="update"
            :title="operation"
        ></PreReviewOpinion>
        <OnlineReviewOpinion
            :visible.sync="onlineReviewOpinionVisible"
            :meetingId="meetingId"
            @update="update"
        ></OnlineReviewOpinion>
        <MeetingQuality
            :visible.sync="meetingQualityVisible"
            :meetingId="meetingId"
        ></MeetingQuality>
    </div>
</template>
<script>
import MeetingUpdate from '../modals/meetingUpdate';
import MeetingMinutes from '../modals/MeetingMinutes';
import PreReviewOpinion from '../modals/PreReviewOpinion';
import OnlineReviewOpinion from '../modals/OnlineReviewOpinion';
import MeetingQuality from '../modals/MeetingQuality';
import {
    getUserAccount,
    generatePDF,
    isOrganizerOrWriter
} from '../../commonFunction';
import {
    handleQualityInfoData,
    getShareLink as externalGetShareLink,
    createOnlineObjectSpanMethod,
    createMinutesObjectSpanMethod,
    createQualityReviewObjectSpanMethod
} from './method';

export default {
    name: 'MeetingDetail',
    components: {
        MeetingUpdate,
        MeetingMinutes,
        PreReviewOpinion,
        OnlineReviewOpinion,
        MeetingQuality
    },
    data() {
        return {
            meetingId: '',
            // 当前用户的域账号
            user: '',
            // 会议基本信息
            meetingInfo: {},
            // 所有参会的人的列表
            meetingPartRelateList: [],
            // 关联项目id列表
            assProjectIdList: [],
            // 关联的所有会议列表
            relatedMeetingList: [],
            // 评委列表
            leftCommittee: [],
            rightCommittee: [],
            // 参会人员列表
            attendacePeople: [],
            // 关联项目列表
            assProjectNameList: [],
            // 操作
            operation: '',
            // 会前预审意见列表
            preReviewList: [],
            // 线上评审意见列表
            onlineReviewList: [],
            // 会议纪要列表
            minutesList: [],
            // 会议效果评价表格
            qualityInfoList: [],
            // 会议数据管理员专有的编辑权限
            dataManagerPermission:
                this.$store.state.permission.btnDatas.includes(
                    'MeetingDataManagerEditButton'
                ),
            // 是否处在生成pdf状态
            isPrint: false,
            meetingUpdateVisible: false,
            meetingUpdateTitle: '',
            meetingMinutesVisible: false,
            meetingMinutesTitle: '',
            preReviewOpinionVisible: false,
            onlineReviewOpinionVisible: false,
            meetingQualityVisible: false,
            // 是否展示会议纪要表格：为了解决变更会议类型为非评审会议时，
            // 导致“会议结论”表头高度异常的问题
            showMinutesList: false
        };
    },
    computed: {
        // 会议时间
        meetingTime() {
            if (!this.meetingInfo.endTime) {
                if (this.meetingInfo.startTime) {
                    return this.meetingInfo?.meetingDate;
                }
                return '';
            }
            const date = this.meetingInfo?.meetingDate;
            const startTime = this.meetingInfo?.startTime;
            const endTime = this.meetingInfo?.endTime;
            return `${date} ${startTime}-${endTime}`;
        },
        // 组织者
        organizer() {
            const people = this.meetingPartRelateList.filter(
                (i) => i.meetingRole === '会议组织人'
            )[0];
            return people?.userName;
        },
        // 主持人
        host() {
            const people = this.meetingPartRelateList.filter(
                (i) => i.meetingRole === '主持人'
            )[0];
            return people?.userName;
        },
        // 会议纪要编制人
        minutesEditor() {
            if (this.meetingPartRelateList.length === 0) {
                return '';
            }
            const { userName } =
                this.meetingPartRelateList.filter(
                    (i) => i.meetingRole === '会议纪要编制人'
                )[0] || {};
            const time = this.meetingInfo.minutesWriteDate
                ? `[${this.meetingInfo.minutesWriteDate}]`
                : '';
            return `${userName} ${time}`;
        },
        // 会议纪要审核人
        minutesChecker() {
            if (this.meetingPartRelateList.length === 0) {
                return '';
            }
            const { userName } =
                this.meetingPartRelateList.filter(
                    (i) => i.meetingRole === '会议纪要审核人'
                )[0] || {};
            const time = this.meetingInfo.minutesReviewDate
                ? `[${this.meetingInfo.minutesReviewDate}]`
                : '';
            return `${userName} ${time}`;
        },
        // 会议分类（评审会议/一般会议）
        meetingClass() {
            if (
                this.meetingInfo.meetingType === '一级TR评审' ||
                this.meetingInfo.meetingType === '二级TR评审'
            ) {
                return '评审会议';
            }
            return '一般会议';
        },
        // 组织者或编写人是否有编辑权限（在恰当的时机）
        isOrganizerOrWriterHasPermission() {
            // 这里对应点击取消会议和结束无纪要两个操作
            if (
                this.meetingInfo.meetingStatus === '已取消' ||
                (this.meetingInfo.meetingStatus === '结束' &&
                    this.meetingInfo.minutesStatus === '无纪要')
            ) {
                return false;
            }
            const bool = isOrganizerOrWriter(this.meetingPartRelateList);
            return bool;
        },
        // 会议纪要是否审核通过
        isMinutesPass() {
            return (
                this.meetingInfo.minutesStatus === '未关闭（有任务未完成）' ||
                this.meetingInfo.minutesStatus === '全部关闭'
            );
        },
        // 会议效果评价有没有全部完成(没全部完成可编辑)
        isQualityFinish() {
            const data = this.qualityInfoList;
            if (data.length === 0) {
                return true;
            }
            let res = false;
            data.forEach((i) => {
                if (!i.evaluationScore) {
                    res = true;
                }
            });
            return res;
        },
        combinedPermission() {
            return (
                this.isOrganizerOrWriterHasPermission ||
                this.dataManagerPermission
            );
        },
        // 表格合并单元格的方法
        onlineObjectSpanMethod() {
            return createOnlineObjectSpanMethod(this.onlineReviewList);
        },
        minutesObjectSpanMethod() {
            return createMinutesObjectSpanMethod(this.minutesList);
        },
        qualityReviewObjectSpanMethod() {
            return createQualityReviewObjectSpanMethod(this.qualityInfoList);
        }
    },
    watch: {
        // 弹窗关闭之后的查询动作
        meetingUpdateVisible(newVal) {
            !newVal && this.update();
        },
        meetingMinutesVisible(newVal) {
            !newVal && this.update();
        },
        preReviewOpinionVisible(newVal) {
            !newVal && this.update();
        },
        onlineReviewOpinionVisible(newVal) {
            !newVal && this.update();
        },
        meetingQualityVisible(newVal) {
            !newVal && this.update();
        }
    },
    created() {
        this.user = getUserAccount(this);
        const meetingId = this.$route.params?.meeting_id;
        if (meetingId) {
            // 这里对应分享链接的按钮
            this.meetingId = meetingId;
            this.$nextTick(() => {
                this.onlineReviewOpinionVisible = true;
            });
        } else if (this.$route?.query?.id) {
            this.meetingId = this.$route.query.id;
        } else {
            this.$router.push({ path: '/dashboard-index' });
        }
    },
    mounted() {
        this.update();
    },
    activated() {
        this.user = getUserAccount(this);
        const meetingId = this.$route.params?.meeting_id;
        if (meetingId) {
            this.meetingId = meetingId;
            this.$nextTick(() => {
                this.onlineReviewOpinionVisible = true;
            });
        } else if (this.$route?.query?.id) {
            this.meetingId = this.$route.query.id;
        } else {
            this.$router.push({ path: '/dashboard-index' });
        }
    },
    methods: {
        /**
         * 根据不同操作打开不同弹窗
         * @param {String} operation 操作
         */
        openDialog(operation) {
            this.operation = operation;
            if (operation === '创建会议' || operation === '编辑会议') {
                this.meetingUpdateVisible = true;
                this.meetingUpdateTitle = operation;
            } else if (operation === '创建纪要' || operation === '编辑纪要') {
                this.meetingMinutesVisible = true;
                this.meetingMinutesTitle =
                    this.meetingInfo.minutesStatus === '审核中'
                        ? '审核会议纪要'
                        : operation;
            } else if (operation.includes('预审')) {
                this.preReviewOpinionVisible = true;
            } else if (operation.includes('线上评审')) {
                this.onlineReviewOpinionVisible = true;
            } else if (operation === '反馈会议质量') {
                this.meetingQualityVisible = true;
            }
        },
        /**
         * 返回
         */
        goBack() {
            const from = this.$route?.query?.from;
            if (from === 'dashBoard') {
                this.$router.push({
                    path: '/dashboard-index',
                    query: {
                        activeName: 'myMeetings'
                    }
                });
            } else {
                this.$router.back();
            }
        },
        /**
         * 获取会议基本信息
         */
        async getMeetingInfo() {
            if (!this.meetingId) return;
            const api = this.$service.feature.meeting.getMeetingInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    const {
                        meetingInfo,
                        meetingPartRelateList,
                        assProjectIdList,
                        assProjectNameList
                    } = res.body;
                    if (!meetingInfo) {
                        this.$message.error('会议信息不存在');
                        this.$router.push({ path: '/dashboard-index' });
                    }
                    // 会议基本信息
                    this.meetingInfo = meetingInfo;
                    this.meetingPartRelateList = meetingPartRelateList;
                    this.assProjectNameList = assProjectNameList || [];

                    // 评委列表
                    const reviewerList = meetingPartRelateList.filter(
                        (i) => i.meetingRole === '评委'
                    );
                    const len = Math.ceil(reviewerList.length / 2);
                    this.leftCommittee = reviewerList.slice(0, len);
                    this.rightCommittee = reviewerList.slice(len);

                    // 参会人员列表
                    this.attendacePeople = meetingPartRelateList
                        .filter((i) => i.meetingRole === '参会人员')
                        .map((i) => i.userName);

                    // 关联项目ID
                    this.assProjectIdList = assProjectIdList;

                    if (this.meetingInfo.organizeForm === '线下') {
                        // 获取会前预审意见
                        this.getPreReviewList();
                    } else {
                        this.getOnlineReviewData();
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error, '查询会议信息失败');
            }
        },
        /**
         * 获取所有与该会议相关的会议
         */
        async getRelatedMeetings() {
            if (!this.meetingId) return;
            const api = this.$service.feature.meeting.getRelatedMeetings;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.relatedMeetingList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取预审意见
         */
        async getPreReviewList() {
            if (!this.meetingId) return;
            const api = this.$service.feature.preReview.getInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.preReviewList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取线上评审意见
         */
        async getOnlineReviewData() {
            const api = this.$service.feature.onlineReview.getInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.onlineReviewList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取会议纪要信息
         */
        async getMeetingMinutesInfo() {
            this.showMinutesList = false;
            const api = this.$service.feature.meetingMinutes.getInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.minutesList = res.body;
                    this.showMinutesList = true;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取会议效果评价信息
         */
        async getMeetingQualityInfo() {
            const api = this.$service.feature.meetingQuality.getInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    handleQualityInfoData(res.body, this);
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 更新
         */
        update() {
            // 获取会议基本信息
            this.getMeetingInfo();
            // 获取所有与该会议相关的会议
            this.getRelatedMeetings();
            if (this.meetingInfo.organizeForm === '线下') {
                // 获取会前预审意见
                this.getPreReviewList();
            } else {
                this.getOnlineReviewData();
            }
            // 获取会议纪要信息
            this.getMeetingMinutesInfo();
            // 获取会议效果评价
            this.getMeetingQualityInfo();
        },
        /**
         * 会议选择变更
         * @param {String} value 选中的会议id
         */
        handleMeetingChange(value) {
            this.meetingId = value;
            this.update();
        },
        /**
         * 生成pdf
         */
        async getPdf() {
            await this.$nextTick(() => {
                this.isPrint = true;
            });
            generatePDF(this.$refs.content, this.meetingInfo.meetingTitle);
            this.$nextTick(() => {
                this.isPrint = false;
            });
        },
        /**
         * 获取分享链接
         */
        getShareLink() {
            externalGetShareLink(this);
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.mt-15 {
    margin-top: 15px;
}

.pre-line {
    white-space: pre-line;
}

.box-main {
    width: 100%;
    padding: 10px 20px 0 20px;
    background-color: #ffffff;
    height: calc(100vh - 40px);
    overflow: auto;
    position: relative;
}

.header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.meeting-info-card {
    padding: 20px 15px;
}
.meeting-title {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 16px;
    font-weight: bold;
}

.meeting-selector-wrapper {
    margin-bottom: 20px;

    @media (max-width: 768px) {
        margin-bottom: 15px;
    }
}

.meeting-selector-container {
    display: flex;
    align-items: center;
    background: #ffffff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 10px 16px;
    gap: 12px;

    .meeting-icon {
        color: #4680ff;
        font-size: 18px;
        flex-shrink: 0;
    }

    .history-select {
        flex: 1;
        max-width: 600px;

        ::v-deep .el-input {
            .el-input__inner {
                font-weight: 600;
                border: none;
                color: #333;
            }
        }
    }

    @media (max-width: 768px) {
        padding: 8px 12px;
        gap: 8px;

        .meeting-icon {
            font-size: 16px;
        }

        .history-select {
            ::v-deep .el-input .el-input__inner {
                font-size: 14px;
            }
        }

        .meeting-status {
            font-size: 11px;
            padding: 3px 8px;
        }
    }
}

.info-item {
    display: flex;
    min-height: 30px;
    word-break: break-all;
    margin-bottom: 10px;
}

.label {
    background-color: #4680ff;
    color: white;
    padding: 5px;
    min-width: 110px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.info-table .label {
    height: 30px;
}

.content {
    padding: 5px;
    flex: 1;
    white-space: pre-line;
}

.committee-section,
.participants-section,
.related-projects,
.agenda-section {
    margin-bottom: 10px;
    display: flex;
}

.committee-section .content {
    width: 100%;
    display: flex;
    .w-50 {
        width: 50%;
    }
}

.committee-item {
    margin-bottom: 10px;
    display: flex;
    font-size: 13px;
    .status {
        color: #999;
        margin-left: 5px;
    }
    .alternative {
        margin-left: 5px;
        margin-right: auto;
    }
}
.online-review-tag {
    color: #ff0000;
}
.conclusion {
    flex-direction: column;
    background-color: #ffff00;
    color: black;
    font-weight: bold;
    flex: 1;
}

.back-button {
    position: absolute;
    right: 30px;
    top: 25px;
}

.print {
    width: 1000px;
    font-size: 10px;
}

.none-conclusion {
    align-items: center;
    justify-content: flex-start;
    min-width: 100%;
    margin-left: 50px;
}
.header-title {
    justify-content: center;
    position: relative;
    width: 100%;
    .edit-btn {
        color: white;
        position: absolute;
        right: 20px;
    }
    .edit-btn-share-link {
        color: white;
        position: absolute;
        right: 80px;
    }
}
.meeting-status-info-icon {
    padding: 0;
    cursor: pointer;
}

::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
.table {
    border: 1px solid #8c8c8c !important;
}
</style>
