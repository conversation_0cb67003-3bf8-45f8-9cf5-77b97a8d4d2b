<template>
    <el-dialog
        title="部门选择"
        :visible.sync="dialogVisible"
        width="500px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleClose"
    >
        <div class="department-tree-container">
            <el-tree
                ref="departmentTree"
                :data="treeData"
                :props="defaultProps"
                show-checkbox
                node-key="orgCode"
                :default-expand-all="true"
                :check-strictly="false"
                @check-change="handleCheckChange"
            >
            </el-tree>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleConfirm"> 确 定 </el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    orgName: 'DepartmentSelector',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        selectedDepartments: {
            type: Array,
            default: () => []
        },
        treeData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            checkedNodes: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        }
    },
    watch: {
        visible: {
            async handler(newVal) {
                if (newVal) {
                    this.initSelectedDepartments();
                }
            },
            immediate: true
        }
    },
    methods: {
        // 初始化已选择的部门
        initSelectedDepartments() {
            this.$nextTick(() => {
                if (
                    this.selectedDepartments &&
                    this.selectedDepartments.length > 0
                ) {
                    this.$refs.departmentTree.setCheckedKeys(
                        this.selectedDepartments
                    );
                }
            });
        },

        // 处理节点选择变化
        handleCheckChange() {
            this.checkedNodes = this.$refs.departmentTree.getCheckedNodes();
        },

        // 确定按钮
        handleConfirm() {
            const checkedKeys = this.$refs.departmentTree.getCheckedKeys();
            const checkedNodes = this.$refs.departmentTree.getCheckedNodes();

            this.$emit('confirm', {
                keys: checkedKeys,
                nodes: checkedNodes
            });

            this.$emit('update:visible', false);
        },

        // 关闭弹窗
        handleClose() {
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped>
.department-tree-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 10px;
}

.dialog-footer {
    text-align: right;
}

::v-deep .el-tree {
    .el-tree-node__content {
        height: 32px;
        line-height: 32px;
    }

    .el-tree-node__label {
        font-size: 14px;
    }
}
</style>
